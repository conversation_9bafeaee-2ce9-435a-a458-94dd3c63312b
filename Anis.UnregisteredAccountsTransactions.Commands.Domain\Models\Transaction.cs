﻿using Anis.UnregisteredAccountsTransactions.Commands.Domain.Abstractions;
using Anis.UnregisteredAccountsTransactions.Commands.Domain.Commands;
using Anis.UnregisteredAccountsTransactions.Commands.Domain.Events;
using Anis.UnregisteredAccountsTransactions.Commands.Domain.Exceptions;
using Anis.UnregisteredAccountsTransactions.Commands.Domain.Extensions;
using Anis.UnregisteredAccountsTransactions.Commands.Domain.Models.SnapShots;

namespace Anis.UnregisteredAccountsTransactions.Commands.Domain.Models;

public class Transaction : Aggregate<Transaction, Snapshot>, IAggregate
{
    private Transaction() { }

    public static Transaction Register(RequestTransactionRegisterCommand command)
    {
        Transaction transaction = new();

        transaction.ApplyNewChange(command.ToRegisterTransactionRequested(transaction.NextSequence));

        return transaction;
    }

    protected override void Mutate(Event @event)
    {
        switch (@event)
        {
            case RegisterTransactionRequested e: Mutate(e); break;
            default:
                throw new AppException(ExceptionStatusCode.FailedPrecondition, $"Unhandled event type: {@event.GetType().Name}");
        }
    }

    protected void Mutate(RegisterTransactionRequested @event)
    {

    }

    protected override void ApplyChangesFromSnapshot(Snapshot snapshot)
    {
        base.ApplyChangesFromSnapshot(snapshot);
    }
}
