﻿//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Anis.UnregisteredAccountsTransactions.Commands.Domain.Resources {
    using System;
    
    
    /// <summary>
    ///   A strongly-typed resource class, for looking up localized strings, etc.
    /// </summary>
    // This class was auto-generated by the StronglyTypedResourceBuilder
    // class via a tool like ResGen or Visual Studio.
    // To add or remove a member, edit your .ResX file then rerun ResGen
    // with the /str option, or rebuild your VS project.
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Resources.Tools.StronglyTypedResourceBuilder", "17.0.0.0")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    public class Phrases {
        
        private static global::System.Resources.ResourceManager resourceMan;
        
        private static global::System.Globalization.CultureInfo resourceCulture;
        
        [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal Phrases() {
        }
        
        /// <summary>
        ///   Returns the cached ResourceManager instance used by this class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Resources.ResourceManager ResourceManager {
            get {
                if (object.ReferenceEquals(resourceMan, null)) {
                    global::System.Resources.ResourceManager temp = new global::System.Resources.ResourceManager("Anis.UnregisteredAccountsTransactions.Commands.Domain.Resources.Phrases", typeof(Phrases).Assembly);
                    resourceMan = temp;
                }
                return resourceMan;
            }
        }
        
        /// <summary>
        ///   Overrides the current thread's CurrentUICulture property for all
        ///   resource lookups using this strongly typed resource class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Globalization.CultureInfo Culture {
            get {
                return resourceCulture;
            }
            set {
                resourceCulture = value;
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Account already exists.
        /// </summary>
        public static string AccountAlreadyExists {
            get {
                return ResourceManager.GetString("AccountAlreadyExists", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Failed to verify account existence.
        /// </summary>
        public static string FailedToVerifyAccountExistence {
            get {
                return ResourceManager.GetString("FailedToVerifyAccountExistence", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Details must not exceed 500 characters..
        /// </summary>
        public static string InvalidDetails {
            get {
                return ResourceManager.GetString("InvalidDetails", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to phone number format is invalid.
        /// </summary>
        public static string InvalidPhoneNumber {
            get {
                return ResourceManager.GetString("InvalidPhoneNumber", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Transaction already exists.
        /// </summary>
        public static string TransactionAlreadyExists {
            get {
                return ResourceManager.GetString("TransactionAlreadyExists", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Transaction registered successfully .
        /// </summary>
        public static string TransactionRegisteredSuccessfully {
            get {
                return ResourceManager.GetString("TransactionRegisteredSuccessfully", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Value must be greater than zero.
        /// </summary>
        public static string ValidateValue {
            get {
                return ResourceManager.GetString("ValidateValue", resourceCulture);
            }
        }
    }
}
