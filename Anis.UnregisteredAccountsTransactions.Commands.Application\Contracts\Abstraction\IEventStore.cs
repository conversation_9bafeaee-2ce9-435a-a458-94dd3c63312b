﻿using Anis.UnregisteredAccountsTransactions.Commands.Domain.Events;
using Anis.UnregisteredAccountsTransactions.Commands.Domain.Models;
using Anis.UnregisteredAccountsTransactions.Commands.Domain.Models.SnapShots;

namespace Anis.UnregisteredAccountsTransactions.Commands.Application.Contracts.Abstraction;

public interface IEventStore
{
    Task<IReadOnlyList<Event>> GetPaginatedAllAsync(int pageNumber, int pageSize, CancellationToken cancellationToken);

    Task<Snapshot?> GetLatestByAggregateIdAsync(string aggregateId, CancellationToken cancellationToken);

    Task<List<Event>> GetByAggregateIdFromSpecifiedSequenceAsync(string aggregateId, Snapshot? snapshot, CancellationToken cancellationToken);

    Task CommitAsync(Transaction aggregate, CancellationToken cancellationToken);

    Task CommitAsync(IReadOnlyList<Event> events, CancellationToken cancellationToken = default);
}
