﻿using Anis.UnregisteredAccountsTransactions.Commands.Domain.Enums;
using Anis.UnregisteredAccountsTransactions.Commands.Domain.Events;
using Anis.UnregisteredAccountsTransactions.Commands.Domain.Events.Data;

namespace Anis.UnregisteredAccountsTransactions.Commands.Test.Faker.Events
{
    public class RegisterTransactionRequestedFaker : EventFaker<RegisterTransactionRequested, RegisterTransactionRequestedData>
    {
        public RegisterTransactionRequestedFaker()
        {
            RuleFor(x => x.Data, f => new RegisterTransactionRequestedData
            {
                PhoneNumber = "**********",
                Value = f.Random.Decimal(1, 10000),
                Details = new string('a', 400),
                SubscriptionType = SubscriptionType.Personal,
            });
        }

        public RegisterTransactionRequestedFaker WithPhoneNumber(string phoneNumber)
        {
            RuleFor(x => x.Data.PhoneNumber, phoneNumber);

            return this;
        }

        public RegisterTransactionRequestedFaker WithValue(decimal value)
        {
            RuleFor(x => x.Data.Value, value);

            return this;
        }

        public RegisterTransactionRequestedFaker WithAggregateId(string aggregateId)
        {
            RuleFor(x => x.AggregateId, aggregateId);

            return this;
        }
    }
}
