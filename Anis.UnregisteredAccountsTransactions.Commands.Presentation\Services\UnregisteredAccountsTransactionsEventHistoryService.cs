﻿using Anis.UnregisteredAccountsTransactions.Commands.Presentation.Extensions;
using Anis.UnregisteredAccountsTransactions.Commands.Presentation.UnregisteredAccountsTransactionsProto.Rebuild;
using Grpc.Core;
using MediatR;

namespace Anis.UnregisteredAccountsTransactions.Commands.Presentation.Services
{
    public class UnregisteredAccountsTransactionsEventHistoryService(
        ILogger<UnregisteredAccountsTransactionsEventHistoryService> logger,
        IMediator mediator
    ) : UnregisteredAccountsTransactionsEventHistory.UnregisteredAccountsTransactionsEventHistoryBase
    {
        private readonly ILogger<UnregisteredAccountsTransactionsEventHistoryService> _logger = logger;
        private readonly IMediator _mediator = mediator;
        public override async Task<GetEventsResponse> GetEvents(GetEventsRequest request, ServerCallContext context)
        {
            var result = await _mediator.Send(request.ToQuery(), context.CancellationToken);

            return new GetEventsResponse
            {
                Events =
                {
                    result.Select(x => x.ToResponse())
                },
            };
        }
    }
}
