using Anis.UnregisteredAccountsTransactions.Commands.Domain.Events;
using Anis.UnregisteredAccountsTransactions.Commands.Test.Helper;
using Anis.UnregisteredAccountsTransactions.Commands.Test.UnregisteredAccountsTransactionsProto.Rebuild;
using Calzolari.Grpc.Net.Client.Validation;
using Grpc.Core;
using Microsoft.AspNetCore.Mvc.Testing;
using Xunit.Abstractions;

namespace Anis.UnregisteredAccountsTransactions.Commands.Test.Tests.HistoryTests
{
    public class GetEventsTest : IClassFixture<WebApplicationFactory<Program>>
    {
        private readonly WebApplicationFactory<Program> _factory;
        private readonly DatabaseHelper _databaseHelper;
        private readonly GrpcHelper _grpcHelper;

        public GetEventsTest(WebApplicationFactory<Program> factory, ITestOutputHelper helper)
        {
            _factory = factory.WithDefaultConfigurations(helper, services =>
            {
                services.SetUnitTestsIsolatedEnvironment();
            });

            _databaseHelper = new DatabaseHelper(_factory);

            _grpcHelper = new GrpcHelper(_factory);
        }

        [Theory]
        [InlineData(1, 1)]
        public async Task GetEvents_WithValidData_ReturnsSuccess(int pageNumber, int pageSize)
        {
            IReadOnlyList<Event> events = new EventsDataFakerHelpers()
                .GenerateForExistingTransaction()
                .GetEvents();

            await _databaseHelper.AddEventsAsync(events);

            GetEventsResponse result = await _grpcHelper.Send(x => x.GetEventsAsync(new GetEventsRequest
            {
                CurrentPage = pageNumber,
                PageSize = pageSize,
            }));

            AssertEquality.OfGetEvents(events, result, pageNumber, pageSize);
        }

        [Theory]
        [InlineData(-1, 10, nameof(GetEventsRequest.CurrentPage))]
        [InlineData(10, -1, nameof(GetEventsRequest.PageSize))]
        public async Task GetEvents_WithInvalidData_InvalidArgument(int pageNumber, int pageSize, string errorPropertyName)
        {
            RpcException exception = await Assert.ThrowsAsync<RpcException>
                (() => _grpcHelper.Send(x => x.GetEventsAsync(new GetEventsRequest
                {
                    CurrentPage = pageNumber,
                    PageSize = pageSize,
                })).ResponseAsync);

            Assert.Equal(StatusCode.InvalidArgument, exception.StatusCode);

            Assert.Contains(
              exception.GetValidationErrors(),
              e => e.PropertyName.EndsWith(errorPropertyName)
            );
        }
    }
}
