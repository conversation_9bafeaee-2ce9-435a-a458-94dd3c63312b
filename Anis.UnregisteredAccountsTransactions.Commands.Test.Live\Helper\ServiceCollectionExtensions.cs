﻿using Anis.UnregisteredAccountsTransactions.Commands.Infrastructure.Services.ServiceBus;
using Microsoft.Extensions.DependencyInjection;

namespace Anis.UnregisteredAccountsTransactions.Commands.Test.Live.Helper
{
    public static class ServiceCollectionExtensions
    {
        public static void AddServiceBusListener(this IServiceCollection services)
        {
            services.AddOptions<ServiceBusOptions>()
                .BindConfiguration(ServiceBusOptions.ServiceBusLiveTesting)
                .ValidateDataAnnotations()
                .ValidateOnStart();

            services.AddSingleton<CommandListener>();
        }
    }
}
