﻿namespace Anis.UnregisteredAccountsTransactions.Commands.Application.Handlers.GetEvents
{
    public class GetEventsQueryResponse
    {
        public required string Id { get; init; }
        public required string Type { get; init; }
        public required string Data { get; init; }
        public required string AggregateId { get; init; }
        public required string UserId { get; init; }
        public required long Sequence { get; init; }
        public required DateTime DateTime { get; init; }
        public required int Version { get; init; }
    }
}
