syntax = "proto3";
 
package anis.gateway.v1;
 
import "google/protobuf/wrappers.proto";
import "google/protobuf/timestamp.proto";
 
option csharp_namespace = "Anis.UnregisteredAccountsTransactions.Commands.Infrastructure.Services.GrpcServices.Protos";

service Queries {
  rpc VerifyAccountIsExist(VerifyAccountRequest) returns (VerifyAccountResponse);
}
 
message VerifyAccountRequest {
	string phone_number = 1;
}
 
message VerifyAccountResponse {
	bool is_exist = 1;
}