name: Deploy To Production
on:
  workflow_dispatch:
env:
  SERVICE_NAME: anis-unregistered-accounts-transactions-commands
  SERVICE_NAMESPACE: anis-unregistered-accounts-transactions
jobs:
  build-service-image:
    runs-on: ubuntu-latest
    steps:
      - name: "Checkout Code"
        uses: actions/checkout@v3
      - name: "Looking For Check Docker & Deployment File"
        id: check_files
        uses: andstor/file-existence-action@v1
        with:
          files: "Dockerfile, production-k8s-manifest.yaml"
          allow_failure: true
      - name: Azure login
        uses: azure/login@v1.4.3
        with:
          creds: ${{ secrets.AZURE_CREDENTIALS }}
      - name: Build and push image to ACR
        run: az acr build --image ${{ env.SERVICE_NAME }}:${{ github.sha }} --registry ecomly -g Core -f ./Dockerfile ./
  
  deploy-to-production:
    permissions:
      actions: read
      contents: read
      id-token: write
    runs-on: self-hosted
    environment: production
    needs:
      - build-service-image
    steps:
      - name: "Checkout Code"
        uses: actions/checkout@v3
      - uses: cschleiden/replace-tokens@v1
        with:
          files: '["production-k8s-manifest.yaml"]'
          tokenPrefix: ___
          tokenSuffix: ___
        env:
          SERVICE_NAME: ${{ env.SERVICE_NAME }}
          SERVICE_NAMESPACE: ${{ env.SERVICE_NAMESPACE }}
          CONTAINER_REGISTRY: ecomly
          IMAGE_TAG: ${{ github.sha }}
      - name: Azure login
        uses: azure/login@v1.4.3
        with:
          creds: ${{ secrets.AZURE_CREDENTIALS }}
      - uses: Azure/aks-set-context@v3
        with:
          cluster-name: anis-primary
          resource-group: ${{ secrets.CLUSTER_RESOURCE_GROUP }}
      - uses: Azure/k8s-deploy@v4
        name: Deploys application
        with:
          action: deploy
          images: ecomly.azurecr.io/${{ env.SERVICE_NAME }}:${{ github.sha }}
          manifests: |
            production-k8s-manifest.yaml
          namespace: ${{ env.SERVICE_NAMESPACE }}
