﻿using Anis.UnregisteredAccountsTransactions.Commands.Infrastructure.Persistence;
using Microsoft.EntityFrameworkCore;
using Polly;

namespace Anis.UnregisteredAccountsTransactions.Commands.Presentation.Extensions;


public class PollyConfiguration(AppDbContext context, ILogger<PollyConfiguration> logger)
{
    public AsyncPolicy ConfigureRetries() =>

            Policy.Handle<DbUpdateException>()
                .WaitAndRetryAsync([

                    TimeSpan.FromSeconds(1),

                    TimeSpan.FromSeconds(2),

                    TimeSpan.FromSeconds(3),

                ], onRetry: (exception, _, attempt, _) =>
                {

                    context.ChangeTracker.Clear();

                    logger.LogWarning(exception, "Call failed, Retry attempt: {RetryAttempt}", attempt);
                });
}

