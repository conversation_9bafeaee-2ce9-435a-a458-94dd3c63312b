﻿syntax = "proto3";

import "google/protobuf/timestamp.proto";
import "google/protobuf/wrappers.proto";
import "google/protobuf/empty.proto";

option csharp_namespace = "Anis.UnregisteredAccountsTransactions.Commands.Presentation";

package anis.demo_unregistered_accounts_transactions.v1;

service DemoUnregisteredAccountsTransactions
{
    rpc RegisterTransactionRequested (RegisterTransactionRequestedRequest) returns (google.protobuf.Empty);
}

message RegisterTransactionRequestedRequest
{
	string aggregate_id = 1;

    google.protobuf.Timestamp date_time = 2;

	string user_id = 3;

    int32 sequence = 4;

	string phone_number = 5;

	Subscription subscription_type = 6;

	double value = 7;

    google.protobuf.StringValue details = 8;
}

enum Subscription
{
     PERSONAL= 0;
     BUSINESS= 1;
     TREASURY= 2;
}