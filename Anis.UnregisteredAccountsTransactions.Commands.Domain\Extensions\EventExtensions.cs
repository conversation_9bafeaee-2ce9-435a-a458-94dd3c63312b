﻿using Anis.UnregisteredAccountsTransactions.Commands.Domain.Commands;
using Anis.UnregisteredAccountsTransactions.Commands.Domain.Constant;
using Anis.UnregisteredAccountsTransactions.Commands.Domain.Events;
using Anis.UnregisteredAccountsTransactions.Commands.Domain.Events.Data;

namespace Anis.UnregisteredAccountsTransactions.Commands.Domain.Extensions
{
    public static class EventExtensions
    {
        public static RegisterTransactionRequested ToRegisterTransactionRequested(this RequestTransactionRegisterCommand requestTransactionRegisterCommand, int sequence) => new()
        {
            AggregateId = requestTransactionRegisterCommand.TransactionId,
            Sequence = sequence,
            DateTime = DateTime.UtcNow,
            Version = 1,
            UserId = requestTransactionRegisterCommand.UserId,//review todo  Fix 
            Data = new RegisterTransactionRequestedData
            {
                PhoneNumber = requestTransactionRegisterCommand.PhoneNumber,
                Value = (decimal)requestTransactionRegisterCommand.Value,
                SubscriptionType = requestTransactionRegisterCommand.SubscriptionType,
                Details = requestTransactionRegisterCommand.Details
            }
        };
    }
}
