﻿using Anis.UnregisteredAccountsTransactions.Commands.Application.Contracts.Abstraction;
using Anis.UnregisteredAccountsTransactions.Commands.Domain.Constant;
using Anis.UnregisteredAccountsTransactions.Commands.Domain.Events;
using MediatR;
using System.Text.Json;

namespace Anis.UnregisteredAccountsTransactions.Commands.Application.Handlers.GetEvents
{
    public class GetEventsQueryHandler(IEventStore eventStore) : IRequestHandler<GetEventsQuery, List<GetEventsQueryResponse>>
    {
        public async Task<List<GetEventsQueryResponse>> Handle(GetEventsQuery request, CancellationToken cancellationToken)
        {
            IReadOnlyList<Event> events = await eventStore.GetPaginatedAllAsync(request.PageNumber, request.PageSize, cancellationToken);

            return events.Select(x => new GetEventsQueryResponse
            {
                AggregateId = x.AggregateId,
                Data = JsonSerializer.Serialize(((dynamic)x).Data, Const.JsonSerializerOptions),
                DateTime = x.DateTime,
                Id = x.Id.ToString(),
                Sequence = x.Sequence,
                Type = x.GetType().Name,
                UserId = x.UserId,
                Version = x.Version
            }).ToList();
        }
    }
}
