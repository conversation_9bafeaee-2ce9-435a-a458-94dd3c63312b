﻿using Anis.UnregisteredAccountsTransactions.Commands.Domain.Models.SnapShots;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using System.Text.Json;
using System.Text.Json.Serialization;

namespace Anis.UnregisteredAccountsTransactions.Commands.Infrastructure.Persistence.Configuration;

public class BaseSnapshotConfigurations : IEntityTypeConfiguration<Snapshot>
{
    private static readonly JsonSerializerOptions JsonSerializerOptions = new()
    {
        DefaultIgnoreCondition = JsonIgnoreCondition.WhenWritingNull
    };

    public void Configure(EntityTypeBuilder<Snapshot> builder)
    {
        builder.HasKey(e => e.Id);

        builder.HasIndex(e => new { e.AggregateId, e.Sequence }).IsUnique();

        builder.Property(e => e.Data).HasConversion(
             v => JsonSerializer.Serialize(v, JsonSerializerOptions),
             v => JsonSerializer.Deserialize<SnapshotData>(v, JsonSerializerOptions)!
        ).HasColumnName("Data");
    }
}
