syntax = "proto3";

import "google/protobuf/wrappers.proto";

option csharp_namespace = "Anis.UnregisteredAccountsTransactions.Commands.Presentation.Protos";

package anis.unregistered_accounts_transactions_commands.v1;

service UnregisteredAccountsTransactionsCommands {
    rpc RequestTransactionRegister(RequestTransactionRegisterRequest) returns (RequestTransactionRegisterResponse);
}

message RequestTransactionRegisterRequest {
    string transaction_id = 1;

    string phone_number = 2;

    double value = 3;

    google.protobuf.StringValue details = 4;

    Subscription subscription_type = 5;
}

message RequestTransactionRegisterResponse{
    string message = 1;
}

enum Subscription
{
     PERSONAL= 0;
     BUSINESS= 1;
     TREASURY= 2;
}