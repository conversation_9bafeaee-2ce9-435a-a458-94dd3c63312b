﻿using Anis.UnregisteredAccountsTransactions.Commands.Domain.Events;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;

namespace Anis.UnregisteredAccountsTransactions.Commands.Test.Live.Helper
{
    public static class JsonEventFactoryExtension
    {
        public static Event ToEvent(string eventType, string json, ILogger logger)
       => eventType switch
       {
           nameof(RegisterTransactionRequested) => Deserialize<RegisterTransactionRequested>(json),
           _ => throw new ArgumentOutOfRangeException(nameof(eventType), $"Event type {eventType} is not handled."),
       };
        private static TEvent Deserialize<TEvent>(string json)
            => JsonConvert.DeserializeObject<TEvent>(json)
                    ?? throw new ArgumentException("Message deserialization failed", nameof(json));
    }
}
