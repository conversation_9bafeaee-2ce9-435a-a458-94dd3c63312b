﻿using Anis.UnregisteredAccountsTransactions.Commands.Domain.Enums;

namespace Anis.UnregisteredAccountsTransactions.Commands.Domain.Events.Data
{
    public class RegisterTransactionRequestedData
    {
        public required string PhoneNumber { get; init; }

        public required decimal Value { get; init; }

        public string? Details { get; init; }

        public required SubscriptionType SubscriptionType { get; init; }
    }
}
