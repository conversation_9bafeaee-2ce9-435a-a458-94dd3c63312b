﻿using Anis.UnregisteredAccountsTransactions.Commands.Presentation.UnregisteredAccountsTransactionsProto.Rebuild;
using FluentValidation;

namespace Anis.UnregisteredAccountsTransactions.Commands.Presentation.Validation
{
    public class GetEventsRequestValidator : AbstractValidator<GetEventsRequest>
    {
        public GetEventsRequestValidator()
        {
            RuleFor(x => x.PageSize).GreaterThan(0);
            RuleFor(x => x.CurrentPage).GreaterThan(0);
        }
    }
}
