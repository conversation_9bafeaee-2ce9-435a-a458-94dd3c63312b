﻿using Anis.UnregisteredAccountsTransactions.Commands.Application.Handlers.RequestTransactionRegister;
using Microsoft.Extensions.DependencyInjection;

namespace Anis.UnregisteredAccountsTransactions.Commands.Application.Extensions;

public static class RegisterationExtensions
{
    public static void MediatrRegister(this IServiceCollection services)
    {
        services.AddMediatR(cfg => cfg.RegisterServicesFromAssemblyContaining<RequestTransactionRegisterCommandHandler>());
    }
}
