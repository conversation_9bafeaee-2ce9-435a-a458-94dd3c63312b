using Anis.UnregisteredAccountsTransactions.Commands.Domain.Events;
using Anis.UnregisteredAccountsTransactions.Commands.Infrastructure.Services.ServiceBus;
using Google.Protobuf.WellKnownTypes;
using Grpc.Core;

namespace Anis.UnregisteredAccountsTransactions.Commands.Presentation.Services
{
    public class DemoUnregisteredAccountsTransactionsService(DemoServiceBusPublisher publisher) : DemoUnregisteredAccountsTransactions.DemoUnregisteredAccountsTransactionsBase
    {
        public override async Task<Empty> RegisterTransactionRequested(
            RegisterTransactionRequestedRequest request,
            ServerCallContext context
        )
        {
            RegisterTransactionRequested @event = new()
            {
                AggregateId = request.AggregateId,
                DateTime = request.DateTime.ToDateTime(),
                Sequence = request.Sequence,
                Data = new()
                {
                    Details = request.Details,
                    PhoneNumber = request.PhoneNumber,
                    Value = (decimal)request.Value,
                    SubscriptionType = (Domain.Enums.SubscriptionType)request.SubscriptionType,
                },
                UserId = request.UserId,
                Version = 1
            };

            await publisher.PublishEventAsync(@event);

            return new Empty();
        }
    }
}
