﻿using Anis.UnregisteredAccountsTransactions.Commands.Presentation.Extensions;
using Anis.UnregisteredAccountsTransactions.Commands.Presentation.Protos;
using Grpc.Core;
using MediatR;
using Polly;

namespace Anis.UnregisteredAccountsTransactions.Commands.Presentation.Services
{
    public class UnregisteredAccountsTransactionsCommandsService(
        IMediator mediator,
        PollyConfiguration pollyConfiguration
    ) : UnregisteredAccountsTransactionsCommands.UnregisteredAccountsTransactionsCommandsBase
    {
        private readonly IMediator _mediator = mediator;
        private readonly AsyncPolicy _policy = pollyConfiguration.ConfigureRetries();

        public override async Task<RequestTransactionRegisterResponse> RequestTransactionRegister(
            RequestTransactionRegisterRequest request,
            ServerCallContext context
        )
        {
            var accessClaims = context.GetRequiredAccessClaims();

            var command = request.ToCommand(accessClaims);

            string reponse = await _policy.ExecuteAsync(() => _mediator.Send(command, context.CancellationToken));

            return new RequestTransactionRegisterResponse
            {
                Message = reponse
            };
        }
    }
}
