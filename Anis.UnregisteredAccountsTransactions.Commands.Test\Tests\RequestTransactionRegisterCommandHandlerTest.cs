﻿using Anis.UnregisteredAccountsTransactions.Commands.Application.Contracts.GrpcServices;
using Anis.UnregisteredAccountsTransactions.Commands.Domain.Events;
using Anis.UnregisteredAccountsTransactions.Commands.Domain.Models.SnapShots;
using Anis.UnregisteredAccountsTransactions.Commands.Domain.Resources;
using Anis.UnregisteredAccountsTransactions.Commands.Presentation.AccessClaimsProto;
using Anis.UnregisteredAccountsTransactions.Commands.Test.Faker.Events;
using Anis.UnregisteredAccountsTransactions.Commands.Test.Faker.Requests;
using Anis.UnregisteredAccountsTransactions.Commands.Test.Helper;
using Anis.UnregisteredAccountsTransactions.Commands.Test.Protos;
using Calzolari.Grpc.Net.Client.Validation;
using Google.Protobuf;
using Grpc.Core;
using Microsoft.AspNetCore.Mvc.Testing;
using Moq;
using Xunit.Abstractions;

namespace Anis.UnregisteredAccountsTransactions.Commands.Test.Tests
{
    public class RequestTransactionRegisterCommandHandlerTest : IClassFixture<WebApplicationFactory<Program>>
    {
        private readonly Mock<IGrpcClientService> _grpcClientService = new();

        private readonly DatabaseHelper _databaseHelper;

        private readonly GrpcHelper _grpcHelper;

        private readonly WebApplicationFactory<Program> _factory;

        public RequestTransactionRegisterCommandHandlerTest(WebApplicationFactory<Program> factory, ITestOutputHelper helper)
        {
            _factory = factory.WithDefaultConfigurations(helper, services =>
            {
                services.SetUnitTestsIsolatedEnvironment(_grpcClientService.Object);
            });

            _databaseHelper = new DatabaseHelper(_factory);

            _grpcHelper = new GrpcHelper(_factory);
        }

        [Fact]
        public async Task RequestTransactionRegister_SendValidRequest_SaveRegisterTransactionRequestedEvent()
        {
            AccessClaims accessClaims = new AccessClaimsFaker();

            Metadata headers = new()
            {
                {"access-claims-bin", accessClaims.ToByteArray()}
            };

            bool? responsee = false; 

            _grpcClientService.Setup(_grpcClientService => _grpcClientService.VerifyAccountIsExist(It.IsAny<string>()))
                .Returns(Task.FromResult(responsee));

            RequestTransactionRegisterRequest request = new RequestTransactionRegisterRequestFaker()
                .Generate();

            RequestTransactionRegisterResponse response = await _grpcHelper.Send(c => c.RequestTransactionRegisterAsync(request, headers: headers).ResponseAsync);

            List<Event> @event = await _databaseHelper.GetAllAsync(request.TransactionId);

            Snapshot? snapshot = await _databaseHelper.GetLatestSnapshotAsync(request.TransactionId);

            Assert.Null(snapshot);

            AssertEquality.OfRegisterTransactionRequested(@event.Last(), request, response, accessClaims.User.Id);
        }

        [Fact]
        public async Task RequestTransactionRegister_WhenFailedToVerifyAccountExistence_ThrowsFailedPrecondition()
        {
            AccessClaims accessClaims = new AccessClaimsFaker();
            Metadata headers = new()
            {
                {"access-claims-bin", accessClaims.ToByteArray()}
            };

            bool? response = null;

            _grpcClientService.Setup(x => x.VerifyAccountIsExist(It.IsAny<string>()))
                .Returns(Task.FromResult(response));

            RequestTransactionRegisterRequest request = new RequestTransactionRegisterRequestFaker()
                .Generate();

            RpcException exception = await Assert.ThrowsAsync<RpcException>(
                async () => await _grpcHelper.Send(r => r.RequestTransactionRegisterAsync(request, headers: headers).ResponseAsync));

            Assert.Equal(StatusCode.FailedPrecondition, exception.StatusCode);
        }

        [Fact]
        public async Task RequestTransactionRegister_WhenAccountAlreadyExists_ThrowsAlreadyExists()
        {
            AccessClaims accessClaims = new AccessClaimsFaker();
            Metadata headers = new()
            {
                {"access-claims-bin", accessClaims.ToByteArray()}
            };

            bool? response = true;

            _grpcClientService.Setup(x => x.VerifyAccountIsExist(It.IsAny<string>()))
                .Returns(Task.FromResult(response));

            RequestTransactionRegisterRequest request = new RequestTransactionRegisterRequestFaker()
                .Generate();

            RpcException exception = await Assert.ThrowsAsync<RpcException>(
                async () => await _grpcHelper.Send(r => r.RequestTransactionRegisterAsync(request, headers: headers).ResponseAsync));

            Assert.Equal(StatusCode.AlreadyExists, exception.StatusCode);
        }

        [Fact]
        public async Task RequestTransactionRegister_NotFoundAccessClaims_ThrowsUnauthorized()
        {
            RequestTransactionRegisterRequest request = new RequestTransactionRegisterRequestFaker()
                .Generate();

            RpcException exception = await Assert.ThrowsAsync<RpcException>(() =>
                _grpcHelper.Send(c => c.RequestTransactionRegisterAsync(request).ResponseAsync));

            Assert.Equal(StatusCode.Unauthenticated, exception.StatusCode);
        }

        [Theory]
        [InlineData("123456789", true, 100, nameof(RequestTransactionRegisterRequest.PhoneNumber))]
        [InlineData("0934567", true, 150, nameof(RequestTransactionRegisterRequest.PhoneNumber))]
        [InlineData("093456789012", true, 150, nameof(RequestTransactionRegisterRequest.PhoneNumber))]
        [InlineData("08912345678", true, 200, nameof(RequestTransactionRegisterRequest.PhoneNumber))]
        [InlineData("09312345678", true, 200, nameof(RequestTransactionRegisterRequest.PhoneNumber))]
        [InlineData("+21708912345678", true, 1, nameof(RequestTransactionRegisterRequest.PhoneNumber))]
        [InlineData("218093123456", true, 10, nameof(RequestTransactionRegisterRequest.PhoneNumber))]
        [InlineData("abcde09312345678", true, 20, nameof(RequestTransactionRegisterRequest.PhoneNumber))]
        [InlineData("************", true, 30, nameof(RequestTransactionRegisterRequest.PhoneNumber))]
        [InlineData("************", true, 40, nameof(RequestTransactionRegisterRequest.PhoneNumber))]
        [InlineData("+", true, 50, nameof(RequestTransactionRegisterRequest.PhoneNumber))]
        [InlineData("+2180931234567", true, 60, nameof(RequestTransactionRegisterRequest.PhoneNumber))]
        [InlineData("002180931234567", true, 70, nameof(RequestTransactionRegisterRequest.PhoneNumber))]
        [InlineData("+963931234567", true, 80, nameof(RequestTransactionRegisterRequest.PhoneNumber))]
        [InlineData("21809123456", true, 90, nameof(RequestTransactionRegisterRequest.PhoneNumber))]
        [InlineData("21809123456789", true, 100, nameof(RequestTransactionRegisterRequest.PhoneNumber))]
        [InlineData(" ", true, 1100, nameof(RequestTransactionRegisterRequest.PhoneNumber))]
        [InlineData("", true, 1120, nameof(RequestTransactionRegisterRequest.PhoneNumber))]
        [InlineData("0912304255", false, 2000, nameof(RequestTransactionRegisterRequest.Details))]
        [InlineData("0912304255", true, -1,  nameof(RequestTransactionRegisterRequest.Value))]
        [InlineData("0912304255", true, 0, nameof(RequestTransactionRegisterRequest.Value))]
        public async Task RequestTransactionRegister_InvalidData_ThrowsInvalidArgument(
            string phoneNumber, 
            bool isDetailsValid,
            double value,
            string errorPropertyName
        )
        {
            RequestTransactionRegisterRequest request = new RequestTransactionRegisterRequestFaker()
                .RuleFor(x => x.PhoneNumber, f => phoneNumber)
                .RuleFor(x => x.Value, f => value)
                .Generate();

            if (!isDetailsValid)
                request.Details = new string('a', 501);

            RpcException exception = await Assert.ThrowsAsync<RpcException>
               (() => _grpcHelper.Send(x => x.RequestTransactionRegisterAsync(request)).ResponseAsync);

            Assert.Equal(StatusCode.InvalidArgument, exception.StatusCode);

            Assert.Contains(
              exception.GetValidationErrors(),
              e => e.PropertyName.EndsWith(errorPropertyName)
            );
        }

        [Fact]
        public async Task RequestTransactionRegister_Idempotency_ThrowsAlreadyExists()
        {
            AccessClaims accessClaims = new AccessClaimsFaker();

            Metadata headers = new()
            {
                {"access-claims-bin", accessClaims.ToByteArray()}
            };

            _grpcClientService.Setup(x => x.VerifyAccountIsExist(It.IsAny<string>()))
                .ReturnsAsync(false);

            RegisterTransactionRequested @event = new RegisterTransactionRequestedFaker().Generate();

            await _databaseHelper.AddEventAsync(@event);

            RequestTransactionRegisterRequest request = new RequestTransactionRegisterRequestFaker()
                .ForEvent(@event)
                .Generate();

            RpcException exception = await Assert.ThrowsAsync<RpcException>(
                 async () => await _grpcHelper.Send(r => r.RequestTransactionRegisterAsync(request, headers: headers).ResponseAsync));

            IReadOnlyList<Event> events = await _databaseHelper.GetAllAsync(@event.AggregateId);

            Assert.Equal(StatusCode.AlreadyExists, exception.StatusCode);

            Assert.Equal(Phrases.TransactionAlreadyExists, exception.Status.Detail);

            AssertEquality.OfSingleEvent(events, @event);
        }
    }
}
