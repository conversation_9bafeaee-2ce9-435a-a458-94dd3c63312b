﻿using Anis.UnregisteredAccountsTransactions.Commands.Domain.Enums;
using MediatR;

namespace Anis.UnregisteredAccountsTransactions.Commands.Domain.Commands
{
    public class RequestTransactionRegisterCommand : IRequest<string>
    {
        public required string TransactionId { get; init; }

        public required string PhoneNumber { get; init; }

        public required double Value { get; init; }

        public required string? Details { get; init; }

        public required SubscriptionType SubscriptionType { get; init; }

        public required string UserId { get; init; }
    }
}
