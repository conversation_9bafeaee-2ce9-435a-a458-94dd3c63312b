﻿using Anis.UnregisteredAccountsTransactions.Commands.Domain.Resources;
using Anis.UnregisteredAccountsTransactions.Commands.Presentation.Protos;
using FluentValidation;
using System.Text.RegularExpressions;

namespace Anis.UnregisteredAccountsTransactions.Commands.Presentation.Validation
{
    public class RequestTransactionRegisterRequestValidator : AbstractValidator<RequestTransactionRegisterRequest>
    {
        private static readonly Regex PhoneNumberRegex = new(@"^09[1-5]\d{7}$", RegexOptions.Compiled); 

        public RequestTransactionRegisterRequestValidator()
        {
            RuleFor(x => x.PhoneNumber)
                .Must(phoneNumber => IsValidPhoneNumber(phoneNumber))
                .WithMessage(Phrases.InvalidPhoneNumber);

            RuleFor(x => x.Details)
            .MaximumLength(500).WithMessage(Phrases.InvalidDetails);

            RuleFor(x => x.Value)
                .GreaterThan(0).WithMessage(Phrases.ValidateValue);
        }

        private static bool IsValidPhoneNumber(string phoneNumber) => PhoneNumberRegex.IsMatch(phoneNumber);
    }
}
