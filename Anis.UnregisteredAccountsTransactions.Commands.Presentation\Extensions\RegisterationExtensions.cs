﻿using Anis.UnregisteredAccountsTransactions.Commands.Presentation.Interceptors;
using Anis.UnregisteredAccountsTransactions.Commands.Presentation.Validation;
using Calzolari.Grpc.AspNetCore.Validation;

namespace Anis.UnregisteredAccountsTransactions.Commands.Presentation.Extensions;

public static class RegistrationExtensions
{
    public static void AddGrpcWithValidators(this IServiceCollection services)
    {
        services.AddGrpc(options =>
        {
            options.EnableMessageValidation();

            options.Interceptors.Add<HandleErrorInterceptor>();
        });

        AddValidators(services);
    }

    private static void AddValidators(IServiceCollection services)
    {
        services.AddGrpcValidation();

        services.AddValidator<GetEventsRequestValidator>();
        services.AddValidator<RequestTransactionRegisterRequestValidator>();
    }

    public static IServiceCollection AddRetryPolicy(this IServiceCollection services) => services.AddScoped<PollyConfiguration>();

}

