using Anis.UnregisteredAccountsTransactions.Commands.Application.Extensions;
using Anis.UnregisteredAccountsTransactions.Commands.Infrastructure.Extensions;
using Anis.UnregisteredAccountsTransactions.Commands.Infrastructure.Services.GrpcServices.Protos;
using Anis.UnregisteredAccountsTransactions.Commands.Infrastructure.Services.Logger;
using Anis.UnregisteredAccountsTransactions.Commands.Presentation.Extensions;
using Anis.UnregisteredAccountsTransactions.Commands.Presentation.Middleware;
using Anis.UnregisteredAccountsTransactions.Commands.Presentation.Services;
using Serilog;

Log.Logger = LoggerServiceBuilder.Build();

var builder = WebApplication.CreateBuilder(args);

// Add services to the container.
builder.Services.InfrastructureRegister(builder.Configuration);

builder.Services.MediatrRegister();

builder.Services.AddGrpc();

builder.Services.AddGrpcWithValidators();

builder.Services.AddRetryPolicy();

builder.Services.AddGrpcClient<Queries.QueriesClient>((o) =>
{
    o.Address = new Uri(builder.Configuration["ClientUrls:QueriesClient"]!);
});

builder.Host.UseSerilog();

var app = builder.Build();

// Configure the HTTP request pipeline.
app.UseMiddleware<CultureInfoManager>();

app.MapGrpcService<DemoUnregisteredAccountsTransactionsService>();

app.MapGrpcService<UnregisteredAccountsTransactionsCommandsService>();

app.MapGrpcService<UnregisteredAccountsTransactionsEventHistoryService>();

app.MapGet("/", () => "Communication with gRPC endpoints must be made through a gRPC client. To learn how to create a client, visit: https://go.microsoft.com/fwlink/?linkid=2086909");

app.Run();

public partial class Program { }