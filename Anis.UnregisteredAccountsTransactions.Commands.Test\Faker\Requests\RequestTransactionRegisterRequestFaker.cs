﻿using Anis.UnregisteredAccountsTransactions.Commands.Domain.Events;
using Anis.UnregisteredAccountsTransactions.Commands.Test.Protos;
using Bogus;
using Subscription = Anis.UnregisteredAccountsTransactions.Commands.Test.Protos.Subscription;

namespace Anis.UnregisteredAccountsTransactions.Commands.Test.Faker.Requests
{
    public class RequestTransactionRegisterRequestFaker : Faker<RequestTransactionRegisterRequest>
    {
        public RequestTransactionRegisterRequestFaker()
        {
            RuleFor(x => x.TransactionId, x => x.Random.Guid().ToString());
            RuleFor(x => x.PhoneNumber, x => "**********");
            RuleFor(x => x.Value, f => f.Random.Double(1, 10000));
            RuleFor(x => x.Details, x => new string('a', 400));
            RuleFor(x => x.SubscriptionType, Subscription.Personal);
        }

        public RequestTransactionRegisterRequestFaker WithTransactionId(string transactionId)
        {
            RuleFor(x=>x.TransactionId, transactionId);
            return this;
        }

        public RequestTransactionRegisterRequestFaker ForEvent(RegisterTransactionRequested @event)
        {
            RuleFor(x => x.TransactionId, f => @event.AggregateId);
            RuleFor(x => x.PhoneNumber, f => @event.Data.PhoneNumber);
            RuleFor(x => x.Value, f => (double)@event.Data.Value);
            RuleFor(x => x.Details, f => @event.Data.Details);

            return this;
        }
    }
}
