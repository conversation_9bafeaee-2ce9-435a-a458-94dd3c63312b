﻿using Microsoft.Extensions.Configuration;

namespace Anis.UnregisteredAccountsTransactions.Commands.Infrastructure.Extensions;

public class SnapshotConfig
{
    public const string Context = "SnapshotConfig";

    public const string TestingContext = "TestSettings:SnapshotConfig";

    public int SnapshotEventLimit { get; init; }

    public void SetTestingOptions(IConfiguration configuration)
    {
        configuration.GetSection(TestingContext).Bind(this);
    }
}