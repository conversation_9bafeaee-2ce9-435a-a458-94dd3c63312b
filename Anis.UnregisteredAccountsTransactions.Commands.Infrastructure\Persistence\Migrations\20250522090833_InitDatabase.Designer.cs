﻿// <auto-generated />
using System;
using Anis.UnregisteredAccountsTransactions.Commands.Infrastructure.Persistence;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Metadata;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;

#nullable disable

namespace Anis.UnregisteredAccountsTransactions.Commands.Infrastructure.Persistence.Migrations
{
    [DbContext(typeof(AppDbContext))]
    [Migration("20250522090833_InitDatabase")]
    partial class InitDatabase
    {
        /// <inheritdoc />
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "9.0.4")
                .HasAnnotation("Relational:MaxIdentifierLength", 128);

            SqlServerModelBuilderExtensions.UseIdentityColumns(modelBuilder);

            modelBuilder.Entity("Anis.UnregisteredAccountsTransactions.Commands.Domain.Events.Event", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("AggregateId")
                        .IsRequired()
                        .HasColumnType("nvarchar(450)");

                    b.Property<DateTime>("DateTime")
                        .HasColumnType("datetime2");

                    b.Property<string>("EventType")
                        .IsRequired()
                        .HasMaxLength(128)
                        .HasColumnType("nvarchar(128)");

                    b.Property<int>("Sequence")
                        .HasColumnType("int");

                    b.Property<string>("UserId")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("Version")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("AggregateId", "Sequence")
                        .IsUnique();

                    b.ToTable("Events");

                    b.HasDiscriminator<string>("EventType").HasValue("Event");

                    b.UseTphMappingStrategy();
                });

            modelBuilder.Entity("Anis.UnregisteredAccountsTransactions.Commands.Domain.Models.SnapShots.Snapshot", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("AggregateId")
                        .IsRequired()
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("Data")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("Data");

                    b.Property<DateTime>("DateTime")
                        .HasColumnType("datetime2");

                    b.Property<int>("Sequence")
                        .HasColumnType("int");

                    b.Property<int>("Version")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("AggregateId", "Sequence")
                        .IsUnique();

                    b.ToTable("Snapshots");
                });

            modelBuilder.Entity("Anis.UnregisteredAccountsTransactions.Commands.Infrastructure.Entities.OutboxMessage", b =>
                {
                    b.Property<int>("Id")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.ToTable("OutboxMessages");
                });

            modelBuilder.Entity("Anis.UnregisteredAccountsTransactions.Commands.Domain.Events.RegisterTransactionRequested", b =>
                {
                    b.HasBaseType("Anis.UnregisteredAccountsTransactions.Commands.Domain.Events.Event");

                    b.Property<string>("Data")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("Data");

                    b.HasDiscriminator().HasValue("RegisterTransactionRequested");
                });

            modelBuilder.Entity("Anis.UnregisteredAccountsTransactions.Commands.Infrastructure.Entities.OutboxMessage", b =>
                {
                    b.HasOne("Anis.UnregisteredAccountsTransactions.Commands.Domain.Events.Event", "Event")
                        .WithOne()
                        .HasForeignKey("Anis.UnregisteredAccountsTransactions.Commands.Infrastructure.Entities.OutboxMessage", "Id")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Event");
                });
#pragma warning restore 612, 618
        }
    }
}
