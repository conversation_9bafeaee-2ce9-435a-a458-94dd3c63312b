﻿using Anis.UnregisteredAccountsTransactions.Commands.Application.Handlers.GetEvents;
using Anis.UnregisteredAccountsTransactions.Commands.Domain.Commands;
using Anis.UnregisteredAccountsTransactions.Commands.Presentation.AccessClaimsProto;
using Anis.UnregisteredAccountsTransactions.Commands.Presentation.Protos;
using Anis.UnregisteredAccountsTransactions.Commands.Presentation.UnregisteredAccountsTransactionsProto.Rebuild;
using Google.Protobuf.WellKnownTypes;

namespace Anis.UnregisteredAccountsTransactions.Commands.Presentation.Extensions;

public static class CommandExtension
{
    public static GetEventsQuery ToQuery(this GetEventsRequest getEvents)
       => new()
       {
           PageNumber = getEvents.CurrentPage,
           PageSize = getEvents.PageSize
       };

    public static RequestTransactionRegisterCommand ToCommand(this RequestTransactionRegisterRequest request, AccessClaims accessClaims)
       => new()
       {
           TransactionId = request.TransactionId,
           PhoneNumber = request.PhoneNumber,
           Value = request.Value,
           Details = request.Details,
           SubscriptionType = (Domain.Enums.SubscriptionType)request.SubscriptionType,
           UserId = accessClaims.User.Id
       };

    public static EventMessage ToResponse(this GetEventsQueryResponse response)
       => new()
       {
           AggregateId = response.AggregateId.ToString(),
           Data = response.Data,
           DateTime = DateTime.SpecifyKind(response.DateTime, DateTimeKind.Utc).ToTimestamp(),
           Id = response.Id,
           Sequence = response.Sequence,
           Type = response.Type,
           UserId = response.UserId,
           Version = response.Version,
       };
}
