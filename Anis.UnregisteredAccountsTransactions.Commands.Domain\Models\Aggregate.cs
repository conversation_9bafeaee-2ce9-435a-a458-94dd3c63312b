﻿using Anis.UnregisteredAccountsTransactions.Commands.Domain.Abstractions;
using Anis.UnregisteredAccountsTransactions.Commands.Domain.Events;
using Anis.UnregisteredAccountsTransactions.Commands.Domain.Models.SnapShots;

public abstract class Aggregate<T, TSnapshot>
    where T : class, IAggregate
    where TSnapshot : class, ISnapshot
{
    private readonly List<Event> _uncommittedEvents = [];

    public string Id { get; private set; } = string.Empty;
    public int Sequence { get; private set; }
    public int NextSequence => Sequence + 1;

    public IReadOnlyList<Event> GetUncommittedEvents() => _uncommittedEvents;
    public void MarkChangesAsCommitted() => _uncommittedEvents.Clear();

    public static T LoadFromHistory(List<Event> history, TSnapshot? snapshot)
    {
        if (history.Count == 0 && snapshot is null)
            throw new ArgumentOutOfRangeException(nameof(history), "history.Count == 0 && snapshot is null");

        var aggregate = CreateNewInstance();

        if (snapshot is not null)
            ((dynamic)aggregate).ApplyChangesFromSnapshot(snapshot);

        ((dynamic)aggregate).ApplyPreviouslyCommittedChanges(history);

        return aggregate;
    }

    private static T CreateNewInstance()
    {
        var aggregate = (T?)Activator.CreateInstance(typeof(T), nonPublic: true);

        if (aggregate == null)
            throw new InvalidOperationException("Aggregate creation failed");

        return aggregate;
    }

    private void ApplyPreviouslyCommittedChanges(List<Event> events)
        => events.ForEach(@event => ValidateAndApplyChange(@event));

    private void ValidateAndApplyChange(Event @event)
    {
        SetIdAndSequence(@event);

        ValidateEvent(@event);

        Mutate(@event);
    }

    private void SetIdAndSequence(Event @event)
    {
        if (@event.Sequence == 1)
        {
            Id = @event.AggregateId;
        }

        Sequence++;
    }

    private void ValidateEvent(Event @event)
    {
        if (Id == default)
            throw new InvalidOperationException("Id == string.Empty");

        if (@event.Sequence != Sequence)
            throw new InvalidOperationException("@event.Sequence != Sequence");
    }

    protected abstract void Mutate(Event @event);

    protected void ApplyNewChange(Event @event)
    {
        ValidateAndApplyChange(@event);

        _uncommittedEvents.Add(@event);
    }

    protected virtual void ApplyChangesFromSnapshot(TSnapshot snapshot)
    {
        Id = snapshot.AggregateId;

        Sequence = snapshot.Sequence;
    }
}
