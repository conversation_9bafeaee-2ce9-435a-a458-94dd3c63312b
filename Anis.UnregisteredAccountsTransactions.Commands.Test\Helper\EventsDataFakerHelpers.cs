﻿using Anis.UnregisteredAccountsTransactions.Commands.Domain.Events;
using Anis.UnregisteredAccountsTransactions.Commands.Test.Faker.Events;

namespace Anis.UnregisteredAccountsTransactions.Commands.Test.Helper
{
    public class EventsDataFakerHelpers
    {
        public Event this[int index]
        {
            get
            {
                return _events.ToArray()[index];
            }
        }

        private readonly List<Event> _events = new();

        private int _sequence = 1;

        private string aggregateId = Guid.NewGuid().ToString();

        public IReadOnlyList<Event> GetEvents() => _events;

        public TEvent First<TEvent>() where TEvent : Event => (TEvent)_events.First(e => e is TEvent);

        public TEvent Last<TEvent>() where TEvent : Event => (TEvent)_events.Last(e => e is TEvent);


        public EventsDataFakerHelpers GenerateForExistingTransaction()
        {
            RegisterTransactionRequested registerTransactionRequested = new RegisterTransactionRequestedFaker().WithAggregateId(aggregateId).Generate();

            _events.Add(registerTransactionRequested);

            _sequence++;

            return this;
        }

        public string GetTransactionId() => aggregateId;
    }
}
