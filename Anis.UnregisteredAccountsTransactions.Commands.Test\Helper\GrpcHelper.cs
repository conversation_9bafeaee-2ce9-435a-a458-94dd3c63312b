﻿using Anis.UnregisteredAccountsTransactions.Commands.Test.Protos;
using Anis.UnregisteredAccountsTransactions.Commands.Test.UnregisteredAccountsTransactionsProto.Rebuild;
using Microsoft.AspNetCore.Mvc.Testing;

namespace Anis.UnregisteredAccountsTransactions.Commands.Test.Helper
{
    public class GrpcHelper(WebApplicationFactory<Program> factory)
    {
        private readonly WebApplicationFactory<Program> _factory = factory;

        public TResult Send<TResult>(Func<UnregisteredAccountsTransactionsEventHistory.UnregisteredAccountsTransactionsEventHistoryClient, TResult> send)
        {
            UnregisteredAccountsTransactionsEventHistory.UnregisteredAccountsTransactionsEventHistoryClient client = new UnregisteredAccountsTransactionsEventHistory
                .UnregisteredAccountsTransactionsEventHistoryClient(_factory.CreateGrpcChannel());

            return send(client);
        }

        public TResult Send<TResult>(Func<UnregisteredAccountsTransactionsCommands.UnregisteredAccountsTransactionsCommandsClient, TResult> send)
        {
            UnregisteredAccountsTransactionsCommands.UnregisteredAccountsTransactionsCommandsClient client = new UnregisteredAccountsTransactionsCommands
                .UnregisteredAccountsTransactionsCommandsClient(_factory.CreateGrpcChannel());

            return send(client);
        }
    }
}
