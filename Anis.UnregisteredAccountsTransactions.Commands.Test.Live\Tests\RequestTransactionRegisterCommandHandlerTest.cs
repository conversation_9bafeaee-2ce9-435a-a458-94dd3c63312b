﻿using Anis.UnregisteredAccountsTransactions.Commands.Application.Contracts.GrpcServices;
using Anis.UnregisteredAccountsTransactions.Commands.Domain.Events;
using Anis.UnregisteredAccountsTransactions.Commands.Domain.Models.SnapShots;
using Anis.UnregisteredAccountsTransactions.Commands.Presentation.AccessClaimsProto;
using Anis.UnregisteredAccountsTransactions.Commands.Test.Faker.Events;
using Anis.UnregisteredAccountsTransactions.Commands.Test.Faker.Requests;
using Anis.UnregisteredAccountsTransactions.Commands.Test.Helper;
using Anis.UnregisteredAccountsTransactions.Commands.Test.Live.Helper;
using Anis.UnregisteredAccountsTransactions.Commands.Test.Protos;
using Google.Protobuf;
using Grpc.Core;
using Microsoft.AspNetCore.Mvc.Testing;
using Microsoft.Extensions.DependencyInjection;
using Moq;
using Xunit.Abstractions;

namespace Anis.UnregisteredAccountsTransactions.Commands.Test.Tests
{
    public class RequestTransactionRegisterCommandHandlerTest : IClassFixture<WebApplicationFactory<Program>>
    {
        private readonly Mock<IGrpcClientService> _grpcClientService = new();

        private readonly DatabaseHelper _databaseHelper;

        private readonly GrpcHelper _grpcHelper;

        private readonly WebApplicationFactory<Program> _factory;

        public RequestTransactionRegisterCommandHandlerTest(WebApplicationFactory<Program> factory, ITestOutputHelper helper)
        {
            _factory = factory.WithDefaultConfigurations(helper, services =>
            {
                services.AddServiceBusListener();
                services.SetLiveTestsIsolatedEnvironment(_grpcClientService.Object);                
            });

            _databaseHelper = new DatabaseHelper(_factory);

            _grpcHelper = new GrpcHelper(_factory);
        }

        [Fact]
        public async Task RequestTransactionRegister_SendValidRequest_SaveRegisterTransactionRequestedEvent()
        {
            CommandListener listener = _factory.Services.GetRequiredService<CommandListener>();

            AccessClaims accessClaims = new AccessClaimsFaker();

            Metadata headers = new()
            {
                {"access-claims-bin", accessClaims.ToByteArray()}
            };

            _grpcClientService.Setup(_grpcClientService => _grpcClientService.VerifyAccountIsExist(It.IsAny<string>()))
                .ReturnsAsync(false);

            RequestTransactionRegisterRequest request = new RequestTransactionRegisterRequestFaker()
                .Generate();

            RequestTransactionRegisterResponse response = await _grpcHelper.Send(c => c.RequestTransactionRegisterAsync(request, headers: headers).ResponseAsync);

            List<Event> events = await listener.WaitForEventThenCloseAsync(request.TransactionId, 1);

            Snapshot? snapshot = await _databaseHelper.GetLatestSnapshotAsync(request.TransactionId);

            Assert.Null(snapshot);

            AssertEquality.OfRegisterTransactionRequested(events.Last(), request, response,accessClaims.User.Id);
        }
    }
}
