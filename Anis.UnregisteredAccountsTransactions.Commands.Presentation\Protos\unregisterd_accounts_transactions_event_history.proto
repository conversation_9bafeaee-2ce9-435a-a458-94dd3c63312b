syntax = "proto3";

import "google/protobuf/timestamp.proto";

option csharp_namespace = "Anis.UnregisteredAccountsTransactions.Commands.Presentation.UnregisteredAccountsTransactionsProto.Rebuild";

package anis.unregistered_accounts_transactions_commands.v1;

service UnregisteredAccountsTransactionsEventHistory {
	rpc GetEvents(GetEventsRequest) returns (GetEventsResponse);
}

message GetEventsRequest{
	int32 current_page = 1;

	int32 page_size= 2;
}

message GetEventsResponse{
	repeated EventMessage events = 1;
}

message EventMessage {
	string id = 1;

	google.protobuf.Timestamp date_time = 2;

	string data  = 3;

	string type = 4;

	int64 sequence = 5;

	int32 version = 6;

	string aggregate_id = 7;

	string user_id = 8;
}
