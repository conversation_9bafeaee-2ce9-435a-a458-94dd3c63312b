﻿using Anis.UnregisteredAccountsTransactions.Commands.Domain.Events;
using Anis.UnregisteredAccountsTransactions.Commands.Infrastructure.Services.ServiceBus;
using Azure.Messaging.ServiceBus;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using System.Text;

namespace Anis.UnregisteredAccountsTransactions.Commands.Test.Live.Helper
{
    public class CommandListener
    {
        private readonly ServiceBusSessionProcessor _processor;
        private readonly ILogger<CommandListener> _logger;

        public CommandListener(IOptions<ServiceBusOptions> serviceBusOptions, ILogger<CommandListener> logger, IConfiguration configuration)
        {
            ServiceBusClient serviceBusClient = new(configuration.GetConnectionString(ServiceBusOptions.ServiceBus));

            _processor = serviceBusClient.CreateSessionProcessor(
                topicName: serviceBusOptions.Value.TopicName,
                subscriptionName: serviceBusOptions.Value.SubscriptionName,
                options: new ServiceBusSessionProcessorOptions()
                {
                    AutoCompleteMessages = false,
                    MaxConcurrentSessions = 100,
                    MaxConcurrentCallsPerSession = 1,
                    PrefetchCount = 1,
                    ReceiveMode = ServiceBusReceiveMode.PeekLock
                }
            );

            _processor.ProcessMessageAsync += Processor_ProcessMessageAsync;

            _processor.ProcessErrorAsync += Processor_ProcessErrorAsync;

            _processor.StartProcessingAsync();

            _logger = logger;
        }

        private List<Event> Events { get; } = [];

        private Task Processor_ProcessMessageAsync(ProcessSessionMessageEventArgs arg)
        {
            var json = Encoding.UTF8.GetString(arg.Message.Body);

            var @event = JsonEventFactoryExtension.ToEvent(arg.Message.Subject, json, _logger);

            Events.Add(@event);

            return arg.CompleteMessageAsync(arg.Message);
        }

        private Task Processor_ProcessErrorAsync(ProcessErrorEventArgs arg)
        {
            _logger.LogError(arg.Exception, "listener failed");

            throw arg.Exception;
        }

        public async Task<List<Event>> WaitForEventThenCloseAsync(string aggregateId, int expectedEventsCount, int seconds = 30)
        {
            List<Event> events = [];

            while (seconds > 0)
            {
                await Task.Delay(1000);

                events = GetEvents(aggregateId, expectedEventsCount);

                if (events.Count >= expectedEventsCount) break;

                seconds--;
            }

            await CloseAsync();

            return events;
        }

        public Task CloseAsync() => _processor.CloseAsync();

        private List<Event> GetEvents(string aggregateId, int expectedEventsCount)
        {
            var events = Events.Where(
                e => e.AggregateId.ToString() == aggregateId
            ).ToList();

            return events.Count < expectedEventsCount ?
                [] : events;
        }
    }
}
