﻿using Anis.UnregisteredAccountsTransactions.Commands.Infrastructure.Persistence;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;

namespace Anis.UnregisteredAccountsTransactions.Commands.Test.Helper;

public class DbTruncate(IServiceProvider provider) : IHostedService
{
    private readonly IServiceProvider _provider = provider;

    public async Task StartAsync(CancellationToken cancellationToken)
    {
        var scope = _provider.CreateScope();

        var context = scope.ServiceProvider.GetRequiredService<AppDbContext>();

        await context.Database.MigrateAsync(cancellationToken);

        context.Events.RemoveRange(context.Events);

        context.Snapshots.RemoveRange(context.Snapshots);

        context.OutboxMessages.RemoveRange(context.OutboxMessages);

        await context.SaveChangesAsync(cancellationToken);

        context.ChangeTracker.Clear();
    }

    public Task StopAsync(CancellationToken cancellationToken) => Task.CompletedTask;
}