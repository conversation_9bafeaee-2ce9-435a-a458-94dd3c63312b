﻿namespace Anis.UnregisteredAccountsTransactions.Commands.Domain.Models.SnapShots;

public class Snapshot : ISnapshot
{
    private Snapshot() { }

    public static Snapshot Create(Transaction transaction) => new()
    {
        AggregateId = transaction.Id,
        Sequence = transaction.Sequence,
        DateTime = DateTime.UtcNow,
        Data = new SnapshotData
        {

        }
    };

    public int Id { get; init; }

    public required string AggregateId { get; init; }

    public required SnapshotData Data { get; init; }

    public DateTime DateTime { get; init; }

    public int Sequence { get; init; }

    public int Version { get; init; }
}
