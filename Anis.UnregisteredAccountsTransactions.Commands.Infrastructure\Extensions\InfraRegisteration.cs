﻿using Anis.UnregisteredAccountsTransactions.Commands.Application.Contracts.Abstraction;
using Anis.UnregisteredAccountsTransactions.Commands.Application.Contracts.GrpcServices;
using Anis.UnregisteredAccountsTransactions.Commands.Infrastructure.Persistence;
using Anis.UnregisteredAccountsTransactions.Commands.Infrastructure.Services.GrpcServices;
using Anis.UnregisteredAccountsTransactions.Commands.Infrastructure.Services.GrpcServices.Protos;
using Anis.UnregisteredAccountsTransactions.Commands.Infrastructure.Services.ServiceBus;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;

namespace Anis.UnregisteredAccountsTransactions.Commands.Infrastructure.Extensions
{
    public static class InfraRegistration
    {
        public static void InfrastructureRegister(this IServiceCollection services, IConfiguration configuration)
        {
            services.AddDbContext<AppDbContext>(
                option => option.UseSqlServer(configuration.GetConnectionString("Database")));

            services.AddScoped<IEventStore, EventStore>();

            services.AddSingleton<IServiceBusPublisher, ServiceBusPublisher>();

            services.AddSingleton<DemoServiceBusPublisher>();

            services.AddHostedService<DatabaseMigrationHostedService>();

            services.AddHostedService<PendingMessagesPublisher>(); 
            
            services.AddScoped<IGrpcClientService, GrpcClientService>();

            services.AddOptions<ServiceBusOptions>()
                .BindConfiguration(ServiceBusOptions.ServiceBus)
                .ValidateDataAnnotations()
                .ValidateOnStart();

            services.AddConfiguration(configuration);
        }

        private static void AddConfiguration(this IServiceCollection services, IConfiguration configuration)
        {
            var snapshotConfig = new SnapshotConfig();

            configuration.Bind(SnapshotConfig.Context, snapshotConfig);

            services.AddSingleton(snapshotConfig);
        }
    }
}
