<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <UserSecretsId>8ec0ec9a-d227-4587-a86a-3611655fd5fa</UserSecretsId>
    <DockerDefaultTargetOS>Linux</DockerDefaultTargetOS>
  </PropertyGroup>

  <ItemGroup>
    <None Remove="Protos\check_phone_number_exists.proto" />
    <None Remove="Protos\Client\access_claims.proto" />
    <None Remove="Protos\unregisterd_accounts_transactions_event_history.proto" />
    <None Remove="Protos\UnregisteredAccountsTransactionsCommands.proto" />
    <None Remove="Protos\unregistered_accounts_transactions_commands.proto" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Calzolari.Grpc.AspNetCore.Validation" Version="9.0.0" />
    <PackageReference Include="FluentValidation" Version="12.0.0" />
    <PackageReference Include="Grpc.AspNetCore" Version="2.71.0" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="9.0.4">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="Microsoft.VisualStudio.Azure.Containers.Tools.Targets" Version="1.21.0" />
    <PackageReference Include="Polly" Version="8.5.2" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Anis.UnregisteredAccountsTransactions.Commands.Infrastructure\Anis.UnregisteredAccountsTransactions.Commands.Infrastructure.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Protobuf Include="Protos\Client\access_claims.proto" GrpcServices="Client" />
    <Protobuf Include="Protos\demo_unregistered_accounts_transactions.proto" GrpcServices="Server" />
    <Protobuf Include="Protos\unregisterd_accounts_transactions_event_history.proto" GrpcServices="Server" />
    <Protobuf Include="Protos\unregistered_accounts_transactions_commands.proto" GrpcServices="Server" />
  </ItemGroup>

</Project>
