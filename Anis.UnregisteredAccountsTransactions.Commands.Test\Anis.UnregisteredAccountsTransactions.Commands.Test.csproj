﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <IsPackable>false</IsPackable>
  </PropertyGroup>

  <ItemGroup>
    <None Remove="Protos\check_phone_number_exists.proto" />
    <None Remove="Protos\unregistered_accounts_transactions_commands.proto" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Bogus" Version="35.6.3" />
    <PackageReference Include="Calzolari.Grpc.Net.Client.Validation" Version="9.0.0" />
    <PackageReference Include="Microsoft.AspNetCore.Mvc.Testing" Version="9.0.4" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.InMemory" Version="9.0.4" />
    <PackageReference Include="Grpc.Tools" Version="2.72.0">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="coverlet.collector" Version="6.0.4">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="Microsoft.NET.Test.Sdk" Version="17.13.0" />
    <PackageReference Include="Moq" Version="4.20.72" />
    <PackageReference Include="Serilog.Sinks.XUnit" Version="3.0.19" />
    <PackageReference Include="xunit" Version="2.9.3" />
    <PackageReference Include="xunit.runner.visualstudio" Version="3.1.0">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Anis.UnregisteredAccountsTransactions.Commands.Presentation\Anis.UnregisteredAccountsTransactions.Commands.Presentation.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Using Include="Xunit" />
  </ItemGroup>

  <ItemGroup>
    <Protobuf Include="Protos\unregisterd_accounts_transactions_event_history.proto" GrpcServices="Client" />
    <Protobuf Include="Protos\unregistered_accounts_transactions_commands.proto">
      <GrpcServices>Client</GrpcServices>
    </Protobuf>
  </ItemGroup>

</Project>
