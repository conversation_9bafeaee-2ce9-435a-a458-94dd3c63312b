﻿using Microsoft.Extensions.Hosting;

namespace Anis.UnregisteredAccountsTransactions.Commands.Infrastructure.Services.ServiceBus
{
    public class PendingMessagesPublisher(IServiceBusPublisher serviceBusPublisher) : IHostedService
    {
        public Task StartAsync(CancellationToken cancellationToken)
        {
            serviceBusPublisher.StartPublishing();

            return Task.CompletedTask;
        }

        public Task StopAsync(CancellationToken cancellationToken) => Task.CompletedTask;
    }
}
