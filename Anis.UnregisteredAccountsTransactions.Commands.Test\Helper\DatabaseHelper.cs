﻿using Anis.UnregisteredAccountsTransactions.Commands.Application.Contracts.Abstraction;
using Anis.UnregisteredAccountsTransactions.Commands.Domain.Events;
using Anis.UnregisteredAccountsTransactions.Commands.Domain.Models.SnapShots;
using Anis.UnregisteredAccountsTransactions.Commands.Infrastructure.Persistence;
using Microsoft.AspNetCore.Mvc.Testing;
using Microsoft.Extensions.DependencyInjection;

namespace Anis.UnregisteredAccountsTransactions.Commands.Test.Helper
{
    public class DatabaseHelper(WebApplicationFactory<Program> factory)
    {
        public async Task<List<Event>> GetAllAsync(string aggregateId, Snapshot? snapshot = null)
        {
            using IServiceScope scope = factory.Services.CreateScope();

            IEventStore eventStore = scope.ServiceProvider.GetRequiredService<IEventStore>();

            return await eventStore.GetByAggregateIdFromSpecifiedSequenceAsync(aggregateId, snapshot, default);
        }

        public async Task AddEventsAsync(IReadOnlyList<Event> events, CancellationToken cancellationToken = default)
        {
            using var scope = factory.Services.CreateScope();

            var eventStore = scope.ServiceProvider.GetRequiredService<IEventStore>();

            await eventStore.CommitAsync(events, cancellationToken);
        }

        public async Task AddEventAsync(Event @event, CancellationToken cancellationToken = default)
        {
            using var scope = factory.Services.CreateScope();

            var _dbContext = scope.ServiceProvider.GetRequiredService<AppDbContext>();

            _dbContext.ChangeTracker.Clear();

            await _dbContext.AddAsync(@event);

            await _dbContext.SaveChangesAsync();
        }

        public async Task<Snapshot?> GetLatestSnapshotAsync(string aggregateId, CancellationToken cancellationToken = default)
        {
            using var scope = factory.Services.CreateScope();

            var eventStore = scope.ServiceProvider.GetRequiredService<IEventStore>();

            return await eventStore.GetLatestByAggregateIdAsync(aggregateId, cancellationToken);
        }

        public async Task AddSnapshotAsync(Snapshot snapshot, CancellationToken cancellationToken = default)
        {
            using var scope = factory.Services.CreateScope();

            var _dbContext = scope.ServiceProvider.GetRequiredService<AppDbContext>();

            _dbContext.ChangeTracker.Clear();

            await _dbContext.AddAsync(snapshot);

            await _dbContext.SaveChangesAsync();
        }
    }
}
