﻿using Anis.UnregisteredAccountsTransactions.Commands.Presentation.AccessClaimsProto;
using Bogus;

namespace Anis.UnregisteredAccountsTransactions.Commands.Test.Faker.Requests
{
    public class AccessClaimsFaker : Faker<AccessClaims>
    {
        public AccessClaimsFaker()
        {
            RuleFor(x => x.Wallet, x => new WalletData
            {
                Id = x.Random.Guid().ToString(),
                RegionId = x.Random.Guid().ToString()
            });

            RuleFor(x => x.User, x => new UserData
            {
                Id = x.Random.Guid().ToString(),
                Role = new(),
            });

            RuleFor(x => x.Account, x => new AccountData
            {
                Id = x.Random.Guid().ToString(),
                LocationId = x.Random.Guid().ToString(),
                SubscriptionId = x.Random.Guid().ToString(),
                SubscriptionType = SubscriptionType.Business,
                AniscomActivated = true
            });
        }
    }
}
