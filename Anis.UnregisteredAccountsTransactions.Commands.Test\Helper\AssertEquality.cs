﻿using Anis.UnregisteredAccountsTransactions.Commands.Domain.Constant;
using Anis.UnregisteredAccountsTransactions.Commands.Domain.Events;
using Anis.UnregisteredAccountsTransactions.Commands.Domain.Models.SnapShots;
using Anis.UnregisteredAccountsTransactions.Commands.Domain.Resources;
using Anis.UnregisteredAccountsTransactions.Commands.Test.Protos;
using Anis.UnregisteredAccountsTransactions.Commands.Test.UnregisteredAccountsTransactionsProto.Rebuild;
using System.Diagnostics.CodeAnalysis;
using System.Text.Json;

namespace Anis.UnregisteredAccountsTransactions.Commands.Test.Helper;

public static class AssertEquality
{
    public static void OfBoth([NotNull] Snapshot? snapshotBefore, [NotNull] Snapshot? snapshotAfter)
    {
        Assert.NotNull(snapshotBefore);

        Assert.NotNull(snapshotAfter);

        Assert.Equal(snapshotBefore.AggregateId, snapshotAfter.AggregateId);

        Assert.Equal(snapshotBefore.Sequence, snapshotAfter.Sequence);

        Assert.Equal(snapshotBefore.DateTime, snapshotAfter.DateTime, TimeSpan.FromMinutes(1));

        Assert.Equal(snapshotBefore.Version, snapshotAfter.Version);
    }

    public static void OfGetEvents(IReadOnlyList<Event> events, GetEventsResponse result, int pageNumber, int pageSize)
    {
        Assert.Equal(pageSize, result.Events.Count);//todo have to added Assert To pageNumber and PageSize

        foreach (var item in result.Events)
        {
            var @event = events.First(x => x.AggregateId.ToString() == item.AggregateId && x.Sequence == item.Sequence);

            Assert.Equal(@event.UserId, item.UserId);

            Assert.Equal(@event.Version, item.Version);

            Assert.Equal(@event.DateTime, item.DateTime.ToDateTime());

            Assert.Equal(JsonSerializer.Serialize(((dynamic)@event).Data, Const.JsonSerializerOptions), item.Data);
        }
    }

    public static void OfSnapshotAndEvents(List<Event> events, [NotNull] Snapshot? snapshot)
    {
        var lastEvent = events.Last();

        Assert.NotNull(snapshot);

        Assert.Equal(lastEvent.AggregateId, snapshot.AggregateId);

        Assert.Equal(lastEvent.Sequence, snapshot.Sequence);

        var ruleEvent = Assert.IsAssignableFrom<Event>(lastEvent);

        dynamic eventData = ((dynamic)ruleEvent).Data;
    }

    public static void OfRegisterTransactionRequested(
        Event events,
        RequestTransactionRegisterRequest request,
        RequestTransactionRegisterResponse response,
        string userId)
    {
        RegisterTransactionRequested @event = Assert.IsType<RegisterTransactionRequested>(events);

        Assert.Equal(request.TransactionId, @event.AggregateId);

        Assert.Equal(request.PhoneNumber, @event.Data.PhoneNumber);

        Assert.Equal((decimal)request.Value, @event.Data.Value);

        Assert.Equal(request.Details, @event.Data.Details);

        Assert.Equal((Domain.Enums.SubscriptionType)request.SubscriptionType, @event.Data.SubscriptionType);

        Assert.Equal(@event.UserId, userId);

        Assert.Equal(Phrases.TransactionRegisteredSuccessfully, response.Message);
    }

    public static void OfSingleEvent(
    IReadOnlyList<Event> events,
    RegisterTransactionRequested expected)
    {
        Assert.Single(events);

        var actual = Assert.IsType<RegisterTransactionRequested>(events[0]);

        Assert.Equal(expected.AggregateId, actual.AggregateId);
        Assert.Equal(expected.Id, actual.Id);
        Assert.Equal(expected.DateTime, actual.DateTime);
        Assert.Equal(expected.Sequence, actual.Sequence);
        Assert.Equal(expected.UserId, actual.UserId);
        Assert.Equal(expected.Version, actual.Version);

        Assert.Equal(expected.Data.PhoneNumber, actual.Data.PhoneNumber);
        Assert.Equal(expected.Data.Value, actual.Data.Value);
        Assert.Equal(expected.Data.Details, actual.Data.Details);
        Assert.Equal(expected.Data.SubscriptionType, actual.Data.SubscriptionType);
    }
}
