﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="MediatR" Version="12.5.0" />
  </ItemGroup>

  <ItemGroup>
    <Compile Update="Resources\Phrases.Designer.cs">
      <DesignTime>True</DesignTime>
      <AutoGen>True</AutoGen>
      <DependentUpon>Phrases.resx</DependentUpon>
    </Compile>
  </ItemGroup>

  <ItemGroup>
    <EmbeddedResource Update="Resources\Phrases.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>Phrases.Designer.cs</LastGenOutput>
    </EmbeddedResource>
  </ItemGroup>

</Project>
