﻿using System.Text.Json;
using System.Text.Json.Serialization;

namespace Anis.UnregisteredAccountsTransactions.Commands.Test.Live.Helper
{
    public class Constants
    {
        public static readonly JsonSerializerOptions JsonSerializerOptions = new()
        {
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
            DefaultIgnoreCondition = JsonIgnoreCondition.WhenWritingNull
        };
    }
}
