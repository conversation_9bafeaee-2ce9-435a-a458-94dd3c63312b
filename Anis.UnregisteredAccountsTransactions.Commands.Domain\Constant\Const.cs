﻿using System.Text.Json;
using System.Text.Json.Serialization;

namespace Anis.UnregisteredAccountsTransactions.Commands.Domain.Constant
{
    public class Const
    {
        public static readonly JsonSerializerOptions JsonSerializerOptions = new()
        {
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
            DefaultIgnoreCondition = JsonIgnoreCondition.WhenWritingNull
        };

        public const string UnregisteredAccountsTransactions = "UnregisteredAccountsTransactions";
    }
}
