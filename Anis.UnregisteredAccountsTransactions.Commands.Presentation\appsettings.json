{"ConnectionStrings": {"Database": "SECRET", "ServiceBus": "SECRET"}, "ServiceBus": {"TopicName": "anis-unregistered-accounts-transactions-commands", "SubscriptionName": "anis-unregistered-accounts-transactions-queries", "DemoConnectionString": "SECRET"}, "LiveTesting": {"ServiceBus": {"TopicName": "anis-unregistered-accounts-transactions-commands", "SubscriptionName": "anis-unregistered-accounts-transactions-queries"}}, "Serilog": {"MinimumLevel": {"Default": "Information", "Override": {"Microsoft.AspNetCore.Routing": "Warning"}}}, "Logger": {"AppName": "anis-x", "AppNamespace": "anis-x", "WriteToConsole": true, "SeqUrl": ""}, "AllowedHosts": "*", "Kestrel": {"EndpointDefaults": {"Protocols": "Http2"}}, "SnapshotConfig": {"SnapshotEventLimit": 100}, "TestSettings": {"SnapshotConfig": {"SnapshotEventLimit": 3}, "Database": "Data Source=(localdb)\\ProjectModels;Initial Catalog=Anis.UnregisteredAccountsTransactions.Test.Commands;Integrated Security=False;Connect Timeout=30;Encrypt=False;Trust Server Certificate=False;Application Intent=ReadWrite;Multi Subnet Failover=False"}, "ClientUrls": {"QueriesClient": "http://localhost:5000"}}