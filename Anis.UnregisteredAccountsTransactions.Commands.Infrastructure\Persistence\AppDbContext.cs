﻿using Anis.UnregisteredAccountsTransactions.Commands.Domain.Events;
using Anis.UnregisteredAccountsTransactions.Commands.Domain.Events.Data;
using Anis.UnregisteredAccountsTransactions.Commands.Domain.Models.SnapShots;
using Anis.UnregisteredAccountsTransactions.Commands.Infrastructure.Entities;
using Anis.UnregisteredAccountsTransactions.Commands.Infrastructure.Persistence.Configuration;
using Microsoft.EntityFrameworkCore;

namespace Anis.UnregisteredAccountsTransactions.Commands.Infrastructure.Persistence;

public class AppDbContext(DbContextOptions<AppDbContext> options) : DbContext(options)
{
    public DbSet<Event> Events { get; set; }
    public DbSet<Snapshot> Snapshots { get; set; }
    public DbSet<OutboxMessage> OutboxMessages { get; set; }

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        modelBuilder.ApplyConfiguration(new OutboxMessageConfigurations());

        modelBuilder.ApplyConfiguration(new BaseEventConfigurations());

        modelBuilder.ApplyConfiguration(new BaseSnapshotConfigurations());

        modelBuilder.ApplyConfiguration(new GenericEventConfiguration<RegisterTransactionRequested, RegisterTransactionRequestedData>());
    }
}
