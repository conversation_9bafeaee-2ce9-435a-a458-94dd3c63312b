﻿using Anis.UnregisteredAccountsTransactions.Commands.Application.Contracts.GrpcServices;
using Anis.UnregisteredAccountsTransactions.Commands.Infrastructure.Services.GrpcServices.Protos;
using Microsoft.Extensions.Logging;

namespace Anis.UnregisteredAccountsTransactions.Commands.Infrastructure.Services.GrpcServices
{
    public class GrpcClientService(Queries.QueriesClient queriesClient, ILogger<GrpcClientService> logger) : IGrpcClientService
    {
        private readonly Queries.QueriesClient _queriesClient = queriesClient;
        private readonly ILogger<GrpcClientService> _logger = logger;

        public async Task<bool?> VerifyAccountIsExist(string phoneNumber)
        {
            try
            {
                var response = await _queriesClient.VerifyAccountIsExistAsync(new VerifyAccountRequest 
                {
                    PhoneNumber =  phoneNumber                    
                });

                return response.IsExist;
            }
            catch (Exception e)
            {
                _logger.LogCritical(e, "Failed to verify account {phoneNumber}", phoneNumber);

                return null;
            }

        }
        
    }
}
