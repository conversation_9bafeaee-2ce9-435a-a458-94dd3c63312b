﻿using Anis.UnregisteredAccountsTransactions.Commands.Application.Contracts.Abstraction;
using Anis.UnregisteredAccountsTransactions.Commands.Application.Contracts.GrpcServices;
using Anis.UnregisteredAccountsTransactions.Commands.Domain.Commands;
using Anis.UnregisteredAccountsTransactions.Commands.Domain.Events;
using Anis.UnregisteredAccountsTransactions.Commands.Domain.Exceptions;
using Anis.UnregisteredAccountsTransactions.Commands.Domain.Models;
using Anis.UnregisteredAccountsTransactions.Commands.Domain.Models.SnapShots;
using Anis.UnregisteredAccountsTransactions.Commands.Domain.Resources;
using MediatR;

namespace Anis.UnregisteredAccountsTransactions.Commands.Application.Handlers.RequestTransactionRegister
{
    public class RequestTransactionRegisterCommandHandler(
        IEventStore eventStore,
        IGrpcClientService grpcClientService) : IRequestHandler<RequestTransactionRegisterCommand, string>
    {
        public async Task<string> Handle(RequestTransactionRegisterCommand command, CancellationToken cancellationToken)
        {
            bool? isAccountExist = await grpcClientService.VerifyAccountIsExist(command.PhoneNumber);

            if(!isAccountExist.HasValue) 
                throw new AppException(ExceptionStatusCode.FailedPrecondition, Phrases.FailedToVerifyAccountExistence);

            if(isAccountExist.Value)
                throw new AppException(ExceptionStatusCode.AlreadyExists, Phrases.AccountAlreadyExists);

            Snapshot? snapshot = await eventStore.GetLatestByAggregateIdAsync(
                command.TransactionId,
                cancellationToken
            );

            List<Event> events = await eventStore.GetByAggregateIdFromSpecifiedSequenceAsync(command.TransactionId, snapshot, cancellationToken);

            if (events.Any() || snapshot is not null)
                throw new AppException(ExceptionStatusCode.AlreadyExists, Phrases.TransactionAlreadyExists);

            Transaction transaction = Transaction.Register(command);

            await eventStore.CommitAsync(transaction, cancellationToken);

            return Phrases.TransactionRegisteredSuccessfully;
        }
    }
}
